import type { <PERSON>a, StoryObj } from "@storybook/react";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { PatientFormsTab } from "./PatientFormsTab";

// Mock the useClientFormResponses hook
const mockFormResponses = {
	data: {
		data: [
			{
				response_uuid: "b7e6c2e2-1f2a-4c3d-9e2a-123456789abc",
				form_name: "Customer Feedback",
				service_name: "Premium Support",
				station_name: "Station A",
				submitted_at: "2024-06-01T12:34:56.000000Z",
			},
			{
				response_uuid: "c8f7d3f3-2g3b-5d4e-0f3b-234567890def",
				form_name: "Health Assessment",
				service_name: "General Care",
				station_name: "Station B",
				submitted_at: "2024-05-15T09:20:30.000000Z",
			},
			{
				response_uuid: "d9g8e4g4-3h4c-6e5f-1g4c-345678901ghi",
				form_name: "Intake Form",
				service_name: "Mental Health",
				station_name: "Station C",
				submitted_at: "2024-04-20T14:45:12.000000Z",
			},
		],
		meta: {},
	},
	isLoading: false,
	error: null,
	refetch: () => Promise.resolve(),
};

const mockEmptyFormResponses = {
	data: { data: [], meta: {} },
	isLoading: false,
	error: null,
	refetch: () => Promise.resolve(),
};

const mockLoadingFormResponses = {
	data: undefined,
	isLoading: true,
	error: null,
	refetch: () => Promise.resolve(),
};

const mockErrorFormResponses = {
	data: undefined,
	isLoading: false,
	error: new Error("Failed to load forms"),
	refetch: () => Promise.resolve(),
};

// Mock the hook
jest.mock("@/hooks/useClients", () => ({
	useClientFormResponses: jest.fn(),
}));

const meta: Meta<typeof PatientFormsTab> = {
	title: "Dashboard/Patient/PatientFormsTab",
	component: PatientFormsTab,
	parameters: {
		layout: "padded",
	},
	decorators: [
		(Story) => {
			const queryClient = new QueryClient({
				defaultOptions: {
					queries: {
						retry: false,
					},
				},
			});
			return (
				<QueryClientProvider client={queryClient}>
					<div className="max-w-md">
						<Story />
					</div>
				</QueryClientProvider>
			);
		},
	],
	argTypes: {
		clientId: {
			control: "text",
			description: "The client ID to fetch form responses for",
		},
	},
};

export default meta;
type Story = StoryObj<typeof meta>;

export const WithFormResponses: Story = {
	args: {
		clientId: "123",
	},
	beforeEach: () => {
		const { useClientFormResponses } = require("@/hooks/useClients");
		useClientFormResponses.mockReturnValue(mockFormResponses);
	},
};

export const EmptyState: Story = {
	args: {
		clientId: "123",
	},
	beforeEach: () => {
		const { useClientFormResponses } = require("@/hooks/useClients");
		useClientFormResponses.mockReturnValue(mockEmptyFormResponses);
	},
};

export const LoadingState: Story = {
	args: {
		clientId: "123",
	},
	beforeEach: () => {
		const { useClientFormResponses } = require("@/hooks/useClients");
		useClientFormResponses.mockReturnValue(mockLoadingFormResponses);
	},
};

export const ErrorState: Story = {
	args: {
		clientId: "123",
	},
	beforeEach: () => {
		const { useClientFormResponses } = require("@/hooks/useClients");
		useClientFormResponses.mockReturnValue(mockErrorFormResponses);
	},
};

export const NoClientId: Story = {
	args: {
		clientId: undefined,
	},
	beforeEach: () => {
		const { useClientFormResponses } = require("@/hooks/useClients");
		useClientFormResponses.mockReturnValue(mockEmptyFormResponses);
	},
};
