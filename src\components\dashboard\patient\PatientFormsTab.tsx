import React from "react";
import { Button } from "@/components/ui/button";
import {
	FileText,
	Trash2,
	Store,
	CalendarClock,
	RefreshCcw,
} from "lucide-react";
import { useClientFormResponses } from "@/hooks/useClients";

interface PatientFormsTabProps {
	clientId?: string | number;
}

export const PatientFormsTab: React.FC<PatientFormsTabProps> = ({
	clientId,
}) => {
	const {
		data: formResponsesData,
		isLoading,
		error,
		refetch,
	} = useClientFormResponses(clientId || "", {
		enabled: !!clientId,
	});

	if (isLoading) {
		return (
			<div className="flex min-h-[200px] items-center justify-center">
				<div className="text-center">
					<RefreshCcw className="mx-auto h-6 w-6 animate-spin text-gray-400" />
					<p className="mt-2 text-sm text-gray-500">
						Loading forms...
					</p>
				</div>
			</div>
		);
	}

	if (error) {
		return (
			<div className="flex min-h-[200px] items-center justify-center">
				<div className="text-center">
					<p className="text-sm text-red-600">Failed to load forms</p>
					<Button
						variant="outline"
						onClick={() => refetch()}
						className="mt-2"
						size="sm"
					>
						Try Again
					</Button>
				</div>
			</div>
		);
	}

	const formResponses = formResponsesData?.data || [];

	if (formResponses.length === 0) {
		return (
			<div className="flex min-h-[200px] items-center justify-center">
				<div className="text-center">
					<FileText className="mx-auto h-8 w-8 text-gray-400" />
					<p className="mt-2 text-sm text-gray-500">No forms found</p>
					<p className="text-xs text-gray-400">
						This patient hasn't submitted any forms yet.
					</p>
				</div>
			</div>
		);
	}

	const formatDate = (dateString: string) => {
		try {
			const date = new Date(dateString);
			if (isNaN(date.getTime())) {
				return dateString;
			}
			return date.toLocaleDateString("en-US", {
				day: "2-digit",
				month: "short",
				year: "numeric",
			});
		} catch {
			return dateString;
		}
	};

	const handleViewForm = (responseUuid: string) => {
		// TODO: Implement form viewing functionality
		console.log("View form:", responseUuid);
	};

	const handleDeleteForm = (responseUuid: string) => {
		// TODO: Implement form deletion functionality
		console.log("Delete form:", responseUuid);
	};

	return (
		<div className="flex w-full flex-col">
			{formResponses.map((formResponse, index) => (
				<div
					key={formResponse.response_uuid || index}
					className="border-t border-gray-200 py-3"
				>
					<div className="flex items-center justify-between">
						<div className="text-sm font-medium">
							{formResponse.form_name}
						</div>
						<div className="flex gap-2">
							<Button
								variant="outline"
								size="icon"
								className="h-8 w-8 p-2"
								title="View Form"
								onClick={() =>
									handleViewForm(formResponse.response_uuid)
								}
							>
								<FileText className="h-3 w-3" />
							</Button>
							<Button
								variant="outline"
								size="icon"
								className="h-8 w-8 p-2"
								title="Delete Form"
								onClick={() =>
									handleDeleteForm(formResponse.response_uuid)
								}
							>
								<Trash2 className="h-3 w-3" />
							</Button>
						</div>
					</div>
					<div className="mt-2 flex items-center gap-4">
						{formResponse.service_name && (
							<div className="rounded-md bg-gray-100 px-2 py-1">
								<span className="text-[10px] font-medium">
									{formResponse.service_name}
								</span>
							</div>
						)}
						{formResponse.station_name && (
							<div className="flex items-center gap-2 text-[#71717A]">
								<Store className="h-2.5 w-2.5" />
								<span className="text-[10px]">
									{formResponse.station_name}
								</span>
							</div>
						)}
						<div className="flex items-center gap-2">
							<CalendarClock className="h-2.5 w-2.5" />
							<span className="text-[10px]">
								{formatDate(formResponse.submitted_at)}
							</span>
						</div>
					</div>
				</div>
			))}
		</div>
	);
};
